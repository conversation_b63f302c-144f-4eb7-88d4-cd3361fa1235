"""
RCMU API функциялар модули
"""
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from ..models import RcmuData
from ..map_data_utils import get_formatted_bs_data
import json


def rcmu_api_data(request):
    """
    API для получения данных РЦМУ для карты
    """
    # Проверка прав доступа (картада ишлаши учун вақтинча ўчирилди)
    # if not check_rcmu_permission(request.user):
    #     return JsonResponse({'error': 'У вас нет прав доступа к этой функции.'}, status=403)
    
    # Получаем только записи с координатами
    rcmu_data = RcmuData.objects.filter(lat__isnull=False, lon__isnull=False).exclude(lat='', lon='')
    
    data = []
    for item in rcmu_data:
        data.append({
            'id': item.id,
            'site': item.site,
            'bs_name': item.bs_name,
            'lat': item.lat.replace(',', '.') if item.lat else None,
            'lon': item.lon.replace(',', '.') if item.lon else None,
            'region': item.region,
            'region_id': item.region_id,
            'branch': item.branch,
            'cause_ru': item.cause_ru,
            'cause_eng': item.cause_eng,
            'responsible': item.responsible,
            'quality_comment': item.quality_comment,
            'date_lego': item.date_lego.strftime('%Y-%m-%d') if item.date_lego else None,
        })
    
    return JsonResponse({'rcmu_data': data})


@login_required
def get_active_clusters(request):
    """
    BS Down Zones панелидаги жорий актив кластерлар рўйхатини қайтариш
    """
    try:
        # Жорий БС маълумотларини олиш
        bs_stations = get_formatted_bs_data(skip_alarms=True)

        # Фақат off статусдаги БС ларни олиш
        off_stations = []
        for station in bs_stations:
            if station.get('status') is True:  # status === true демак off
                off_stations.append(station)

        # Кластерлаш логикаси (off-stations-clustering.js дан олинган)
        active_clusters = cluster_off_stations(off_stations)

        # Кластерлар маълумотларини форматлаш
        clusters_data = []
        for cluster in active_clusters:
            # Group ID ни генерация қилиш
            group_id = generate_group_id(cluster)

            clusters_data.append({
                'group_id': group_id,
                'stations_count': len(cluster['stations']),
                'area_name': cluster['area']['area'],
                'region_name': cluster['area']['region'],
                'center_lat': cluster['center']['lat'],
                'center_lon': cluster['center']['lon'],
                'stations': [
                    {
                        'id': station.get('id'),
                        'name': station.get('name'),
                        'bsName': station.get('bsName', station.get('name')),
                        'lat': station.get('lat'),
                        'lon': station.get('lon')
                    }
                    for station in cluster['stations']
                ]
            })

        return JsonResponse({
            'success': True,
            'active_clusters': clusters_data,
            'total_clusters': len(clusters_data),
            'total_off_stations': len(off_stations)
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)


def cluster_off_stations(stations):
    """
    БС ларни қўшнилик бўйича группалаш (JavaScript логикасидан олинган)
    """
    MIN_STATIONS_TO_SHOW = 3

    if len(stations) < MIN_STATIONS_TO_SHOW:
        return []

    # Содда кластерлаш - фақат яқинлик бўйича
    components = simple_cluster_by_proximity(stations)

    # Группаларни шакллантириш
    groups = []
    for component in components:
        if len(component) >= MIN_STATIONS_TO_SHOW:
            groups.append({
                'stations': component,
                'center': calculate_group_center(component),
                'area': get_group_area(component)
            })

    return groups


def simple_cluster_by_proximity(stations):
    """
    Содда кластерлаш - фақат яқинлик бўйича
    """
    visited = set()
    clusters = []

    for i, station in enumerate(stations):
        if i in visited:
            continue

        cluster = []
        to_visit = [i]

        while to_visit:
            current_index = to_visit.pop()
            if current_index in visited:
                continue

            visited.add(current_index)
            cluster.append(stations[current_index])

            # Жорий БС га яқин бошқа OFF БС ларни излаш
            for j, other_station in enumerate(stations):
                if j not in visited:
                    distance = calculate_distance(
                        stations[current_index].get('lat', 0),
                        stations[current_index].get('lon', 0),
                        other_station.get('lat', 0),
                        other_station.get('lon', 0)
                    )

                    # 5 км ичидаги OFF БС лар автоматик бир кластерда
                    if distance <= 5000:
                        to_visit.append(j)

        if cluster:
            clusters.append(cluster)

    return clusters


def calculate_distance(lat1, lon1, lat2, lon2):
    """
    Икки нуқта орасидаги масофани ҳисоблаш (метрларда)
    """
    import math

    # Координаталарни float га айлантириш
    try:
        lat1, lon1, lat2, lon2 = float(lat1), float(lon1), float(lat2), float(lon2)
    except (ValueError, TypeError):
        return float('inf')

    # Haversine формуласи
    R = 6371000  # Ер радиуси метрларда

    lat1_rad = math.radians(lat1)
    lat2_rad = math.radians(lat2)
    delta_lat = math.radians(lat2 - lat1)
    delta_lon = math.radians(lon2 - lon1)

    a = (math.sin(delta_lat / 2) ** 2 +
         math.cos(lat1_rad) * math.cos(lat2_rad) *
         math.sin(delta_lon / 2) ** 2)
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))

    return R * c


def calculate_group_center(stations):
    """
    Группа марказини ҳисоблаш
    """
    if not stations:
        return {'lat': 0, 'lon': 0}

    total_lat = 0
    total_lon = 0
    valid_count = 0

    for station in stations:
        try:
            lat = float(station.get('lat', 0))
            lon = float(station.get('lon', 0))
            if lat != 0 and lon != 0:
                total_lat += lat
                total_lon += lon
                valid_count += 1
        except (ValueError, TypeError):
            continue

    if valid_count == 0:
        return {'lat': 0, 'lon': 0}

    return {
        'lat': total_lat / valid_count,
        'lon': total_lon / valid_count
    }


def get_group_area(stations):
    """
    Группа ҳудудини аниқлаш
    """
    if not stations:
        return {'area': 'Неизвестно', 'region': 'Неизвестно'}

    # Биринчи БС дан маълумот олиш
    first_station = stations[0]
    return {
        'area': first_station.get('area_name', 'Неизвестно'),
        'region': first_station.get('region_name', 'Неизвестно')
    }


def generate_group_id(cluster):
    """
    Кластер учун уникал ID генерация қилиш
    """
    import hashlib

    # Кластердаги БС лар ID ларини сортлаб, хеш ясаш
    station_ids = sorted([str(station.get('id', '')) for station in cluster['stations']])
    id_string = '_'.join(station_ids)

    # MD5 хеш ясаш
    hash_object = hashlib.md5(id_string.encode())
    return f"cluster_{hash_object.hexdigest()[:8]}"