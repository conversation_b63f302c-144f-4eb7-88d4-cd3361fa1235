/* Custom styles for the RCMU Treez page */

.bs-down-container {
    padding: 20px;
}

.bs-down-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.clickable-row {
    cursor: pointer;
}

.stations-list {
    font-size: 0.85rem;
    color: #6c757d;
    max-width: 250px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

tr.filters th {
    padding: 8px;
    vertical-align: top;
}

.table-dark .filters .form-control {
    background-color: #454d55;
    border-color: #6c757d;
    color: #fff;
}

.table-dark .filters .form-control::placeholder {
    color: #adb5bd;
}

.table-dark .filters .form-control:focus {
    background-color: #454d55;
    border-color: #86b7fe;
    color: #fff;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.actions .btn {
    transition: all 0.2s ease;
}

.actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

/* BS Down Zones синхронизация стиллари */
.clickable-row.table-danger {
    background-color: #f8d7da !important;
    border-color: #f5c2c7 !important;
    color: #842029 !important;
}

.clickable-row.table-danger:hover {
    background-color: #f1aeb5 !important;
    color: #842029 !important;
}

.clickable-row.table-secondary {
    background-color: #e2e3e5 !important;
    border-color: #d3d6d8 !important;
    color: #41464b !important;
}

.clickable-row.table-secondary:hover {
    background-color: #d3d6d8 !important;
    color: #41464b !important;
}

/* Актив кластерлар учун қўшимча индикатор */
.clickable-row.table-danger .timestamp::before {
    content: "🔴 ";
    font-size: 0.8em;
    margin-right: 5px;
}

/* Ноактив кластерлар учун қўшимча индикатор */
.clickable-row.table-secondary .timestamp::before {
    content: "⚪ ";
    font-size: 0.8em;
    margin-right: 5px;
}

/* Матн контрастини яхшилаш */
.clickable-row.table-danger .reason-text {
    font-weight: 500;
}

.clickable-row.table-secondary .reason-text {
    color: #6c757d !important;
}

/* Transition эффектлари */
.clickable-row {
    transition: all 0.3s ease;
}

/* Синхронизация статуси индикатори */
.sync-status {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 8px 12px;
    background: #28a745;
    color: white;
    border-radius: 4px;
    font-size: 0.8rem;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.sync-status.visible {
    opacity: 1;
}

.sync-status.error {
    background: #dc3545;
}