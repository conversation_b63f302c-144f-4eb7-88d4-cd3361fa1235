{% extends "base.html" %}

{% block title %}BS Down Zones ≥ 3 count - Причины{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="/maps/css/rcmu_treez.css?v=1.1">
{% endblock %}

{% block content %}
<div class="bs-down-container" id="bs-down-container" data-is-admin="{% if user.privilege_id == 1 or user.privilege_id == 6 %}true{% else %}false{% endif %}" data-language-code="{{ LANGUAGE_CODE|default:'ru' }}">
    <div class="bs-down-header">
        <div>
            <h2>BS Down Zones ≥ 3 count - Причины</h2>
            <p class="text-muted mb-0">Управление причинами аварий кластеров</p>
        </div>
        <div>
            <button class="btn btn-primary" onclick="window.location.href='/map/'">
                <i class="fas fa-map"></i> К карте
            </button>
        </div>
    </div>
    
    {% if page_obj.object_list %}
    <div class="table-responsive">
        <table class="table table-striped table-hover reasons-table align-middle">
            <thead class="table-dark">
                <tr>
                    <th scope="col">Время инцидента</th>
                    <th scope="col">Местоположение</th>
                    <th scope="col">Кол-во БС</th>
                    <th scope="col">Станции</th>
                    <th scope="col">Причина</th>
                    <th scope="col">Добавил</th>
                    <th scope="col" style="width: 120px;">Действия</th>
                </tr>
                <tr class="filters">
                    <th><input type="text" class="form-control form-control-sm" placeholder="Фильтр..." name="incident_timestamp" value="{{ request.GET.incident_timestamp|default:'' }}"></th>
                    <th><input type="text" class="form-control form-control-sm" placeholder="Фильтр..." name="location" value="{{ request.GET.location|default:'' }}"></th>
                    <th><input type="text" class="form-control form-control-sm" placeholder="Фильтр..." name="stations_count" value="{{ request.GET.stations_count|default:'' }}"></th>
                    <th><input type="text" class="form-control form-control-sm" placeholder="Фильтр..." name="stations" value="{{ request.GET.stations|default:'' }}"></th>
                    <th><input type="text" class="form-control form-control-sm" placeholder="Фильтр..." name="reason" value="{{ request.GET.reason|default:'' }}"></th>
                    <th><input type="text" class="form-control form-control-sm" placeholder="Фильтр..." name="created_by" value="{{ request.GET.created_by|default:'' }}"></th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                {% for reason in page_obj.object_list %}
                <tr class="clickable-row" 
                    data-reason-id="{{ reason.id }}" 
                    data-reason-ru="{{ reason.reason_ru|default:reason.reason|escapejs }}" 
                    data-reason-en="{{ reason.reason_en|escapejs }}"
                    data-stations="{{ reason.get_stations_list_display|escapejs }}">
                    <td class="timestamp">{{ reason.incident_timestamp|date:"d.m.Y H:i" }}</td>
                    <td class="location-info">{{ reason.get_location_display }}</td>
                    <td><span class="station-count">{{ reason.stations_count }} БС</span></td>
                    <td class="stations-list">{{ reason.get_stations_list_display }}</td>
                    <td class="reason-text" 
                        title="{% if reason.get_reason_for_lang %}{{ reason.get_reason_for_lang }}{% else %}Причина не указана{% endif %}">
                        {% if reason.get_reason_for_lang %}
                            {{ reason.get_reason_for_lang }}
                        {% else %}
                            <span class="text-muted fst-italic">Причина не указана...</span>
                        {% endif %}
                    </td>
                    <td>
                        {% if reason.get_reason_for_lang %}
                            {% if reason.updated_by %}
                                {{ reason.updated_by.username }}
                            {% elif reason.created_by %}
                                {{ reason.created_by.username }}
                            {% else %}
                                -
                            {% endif %}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td class="actions">
                        <button class="btn btn-sm btn-info view-on-map-btn" 
                                title="Показать на карте"
                                data-lat="{{ reason.center_lat }}" 
                                data-lon="{{ reason.center_lon }}">
                            <i class="fas fa-map-marker-alt"></i>
                        </button>
                        {% if user.privilege_id == 1 or user.privilege_id == 6 %}
                        <button class="btn btn-sm btn-danger delete-reason-btn" title="Удалить запись" data-reason-id="{{ reason.id }}">
                            <i class="fas fa-trash"></i>
                        </button>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    {% include "pagination.html" %}
    
    {% else %}
    <div class="empty-state">
        <i class="fas fa-clipboard-list"></i>
        <h4>Нет сохраненных причин</h4>
        <p>Причины будут появляться здесь после их добавления для BS Down кластеров</p>
    </div>
    {% endif %}
    
    <!-- Bootstrap Modal (бу ерда content блокида) -->
    <div class="modal fade" id="editReasonModal" tabindex="-1" aria-labelledby="editReasonModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editReasonModalLabel">Редактировать причину</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="edit-reason-form" novalidate>
                    <div class="modal-body">
                        <div id="modal-cluster-context" class="alert alert-info" style="font-size: 0.9rem; padding: 0.75rem;">
                            {## JS бу ерга кластер маълумотини қўяди ##}
                        </div>
                        <input type="hidden" id="edit-reason-id">
                        <div class="form-group-modal">
                            {## Бу ерга JS динамик равишда инпутларни қўяди ##}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
                        <button type="submit" class="btn btn-primary">Сохранить</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="/maps/js/rcmu_treez.js" defer></script>
{% endblock %} 