document.addEventListener('DOMContentLoaded', () => {
    console.log("RCMU Treez script started (v.<PERSON>tra<PERSON>).");

    // Helper function to get CSRF token
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    const mainContainer = document.getElementById('bs-down-container');
    if (!mainContainer) {
        console.error('Error: Main container #bs-down-container not found.');
        return;
    }

    const isAdmin = mainContainer.dataset.isAdmin === 'true';
    const currentLang = mainContainer.dataset.languageCode || 'ru';

    // Bootstrap Modal Initialization
    const modalElement = document.getElementById('editReasonModal');
    if (!modalElement) {
        console.error("Bootstrap modal element #editReasonModal not found!");
        return;
    }
    const reasonModal = new bootstrap.Modal(modalElement);

    const editForm = document.getElementById('edit-reason-form');
    const editReasonIdInput = document.getElementById('edit-reason-id');
    const modalBody = modalElement.querySelector('.form-group-modal');
    const modalContext = document.getElementById('modal-cluster-context');

    // --- NEW DEBOUNCED FILTERING LOGIC ---
    let debounceTimer;
    const filterInputs = document.querySelectorAll('tr.filters input[type="text"]');

    filterInputs.forEach(input => {
        input.addEventListener('keyup', () => {
            clearTimeout(debounceTimer);
            debounceTimer = setTimeout(() => {
                applyColumnFilters();
            }, 500); // Wait for 500ms after user stops typing
        });
    });

    function applyColumnFilters() {
        const url = new URL(window.location.href);
        url.searchParams.forEach((_, key) => url.searchParams.delete(key)); // Clear old params

        filterInputs.forEach(input => {
            if (input.value) {
                url.searchParams.set(input.name, input.value);
            }
        });

        window.location.href = url.toString();
    }
    
    // Button handling
    const refreshClustersBtn = document.getElementById('refresh-clusters-btn');
    if (refreshClustersBtn) {
        refreshClustersBtn.addEventListener('click', () => {
            window.location.href = '/map/?open_bs_down=true';
        });
    }
    
    // --- Event Delegation for table buttons ---
    const tableBody = mainContainer.querySelector('.reasons-table tbody');
    if (tableBody) {
        tableBody.addEventListener('click', (event) => {
            const target = event.target;
            
            // Кнопкани аниқлаш - icon тагидан ҳам ишлайди
            const buttonElement = target.closest('.btn');
            if (buttonElement) {
                // Кнопка босилган бўлса, модал очмасдан кнопка функцияни ишлатамиз
                event.preventDefault();
                event.stopPropagation();
                handleButtonClicks(buttonElement);
                return;
            }

            // Агар кнопка босилмаган бўлса, қатор учун модални очамиз
            const clickedRow = target.closest('.clickable-row');
            if (clickedRow) {
                openEditModal(clickedRow);
            }
        });
    }

    // Кнопкалар учун алоҳида функция
    function handleButtonClicks(buttonElement) {
        console.log('Button clicked:', buttonElement.className);
        
        // "Показать на карте" тугмаси
        if (buttonElement.classList.contains('view-on-map-btn')) {
            const lat = buttonElement.dataset.lat;
            const lon = buttonElement.dataset.lon;
            console.log('View on map clicked, lat:', lat, 'lon:', lon);
            if (lat && lon) {
                window.open(`/map/?lat=${lat}&lon=${lon}&zoom=14`, '_blank');
            } else {
                alert('Координаты не найдены для этой записи');
            }
            return;
        }

        // "Удалить" тугмаси
        if (buttonElement.classList.contains('delete-reason-btn')) {
            const reasonId = buttonElement.dataset.reasonId;
            console.log('Delete button clicked, reasonId:', reasonId);
            if (confirm('Вы уверены, что хотите удалить эту запись?')) {
                deleteReason(reasonId, buttonElement.closest('tr'));
            }
            return;
        }
    }

    function openEditModal(row) {
        const reasonId = row.dataset.reasonId;
        const reasonRu = row.dataset.reasonRu;
        const reasonEn = row.dataset.reasonEn;
        const stations = row.dataset.stations;
        
        const timestamp = row.querySelector('.timestamp')?.textContent || 'N/A';
        const location = row.querySelector('.location-info')?.textContent || 'N/A';
        const stationCount = row.querySelector('.station-count')?.textContent || 'N/A';
        
        if(modalContext) {
            modalContext.innerHTML = `
                <strong>Время:</strong> ${timestamp}<br>
                <strong>Локация:</strong> ${location}<br>
                <strong>Кол-во БС:</strong> ${stationCount}<br>
                <strong>Станции:</strong> <span style="font-style: italic;">${stations || 'N/A'}</span>
            `;
        }

        editReasonIdInput.value = reasonId;
        
        if (isAdmin) {
            modalBody.innerHTML = `
                <div class="mb-3">
                    <label for="edit-reason-text-ru" class="form-label">Причина аварии (рус)</label>
                    <textarea class="form-control" id="edit-reason-text-ru" required>${reasonRu || ''}</textarea>
                </div>
                <div class="mb-3">
                    <label for="edit-reason-text-en" class="form-label">Reason (eng)</label>
                    <textarea class="form-control" id="edit-reason-text-en">${reasonEn || ''}</textarea>
                </div>
            `;
        } else {
            const reasonText = currentLang === 'en' ? (reasonEn || reasonRu) : reasonRu;
            modalBody.innerHTML = `
                <div class="mb-3">
                    <label for="edit-reason-text" class="form-label">Причина аварии</label>
                    <textarea class="form-control" id="edit-reason-text" required>${reasonText || ''}</textarea>
                </div>
            `;
        }
        reasonModal.show();
    }
    
    // Form submission
    if(editForm) {
        editForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            const reasonId = editReasonIdInput.value;
            let reasonData = {};

            try {
                if (isAdmin) {
                    const reasonRu = document.getElementById('edit-reason-text-ru').value.trim();
                    const reasonEn = document.getElementById('edit-reason-text-en').value.trim();
                    if (!reasonRu && !reasonEn) {
                        alert('Пожалуйста, введите причину хотя бы на одном языке.'); return;
                    }
                    reasonData = { ru: reasonRu, en: reasonEn };
                    await updateReason(reasonId, reasonData);
                } else {
                    const reasonText = document.getElementById('edit-reason-text').value.trim();
                    if (!reasonText) {
                        alert('Пожалуйста, введите причину.'); return;
                    }
                    reasonData[currentLang] = reasonText;
                    await updateReason(reasonId, reasonData);
                }
                
                reasonModal.hide();
                updateTableRow(reasonId, reasonData);
                alert('Причина успешно обновлена.');

            } catch (error) {
                console.error('Error:', error);
                alert(`Произошла ошибка при обновлении: ${error.message}`);
            }
        });
    }

    function updateReason(id, reasonData) {
        // reasonData объекти {ru: '...', en: '...'} ko'rinishida bo'ladi
        return fetch(`/map/rcmu/api/update-reason/${id}/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({ reason_data: reasonData }) // Backend'ga moslashtirish kerak
        }).then(response => {
            if (!response.ok) throw new Error('Network response was not ok.');
            return response.json();
        });
    }

    function updateTableRow(reasonId, reasonData) {
        const row = document.querySelector(`tr[data-reason-id='${reasonId}']`);
        if (row) {
            const reasonCell = row.querySelector('.reason-text');
            const newReasonText = reasonData[currentLang] || reasonData.ru || reasonData.en || '';
            
            if (newReasonText) {
                reasonCell.innerHTML = newReasonText;
                reasonCell.title = newReasonText;
                reasonCell.classList.remove('text-muted', 'fst-italic');
            } else {
                reasonCell.innerHTML = `<span class="text-muted fst-italic">Причина не указана...</span>`;
                reasonCell.title = 'Причина не указана';
            }
            
            // data-atributlarni ham yangilaymiz
            if(reasonData.ru) row.dataset.reasonRu = reasonData.ru;
            if(reasonData.en) row.dataset.reasonEn = reasonData.en;
        }
    }

    async function deleteReason(id, row) {
        try {
            const response = await fetch(`/map/rcmu/api/delete-reason/${id}/`, {
                method: 'DELETE',
                headers: { 'X-CSRFToken': getCookie('csrftoken') }
            });
            const data = await response.json();
            if (data.success) {
                alert('Причина успешно удалена');
                row.remove(); // Удаляем строку из таблицы
            } else {
                alert('Ошибка: ' + data.error);
            }
        } catch (error) {
            console.error('Error:', error);
            alert('Произошла ошибка при удалении');
        }
    }

    // Force cursor style after everything else has loaded
    window.addEventListener('load', () => {
        const rows = document.querySelectorAll('.clickable-row');
        rows.forEach(row => {
            const cells = row.getElementsByTagName('td');
            for(let cell of cells) {
                // Кнопкалар учун курсор ўзгартирмаймиз
                if (!cell.querySelector('.btn')) {
                    cell.style.cursor = 'pointer';
                }
            }
        });
    });
}); 